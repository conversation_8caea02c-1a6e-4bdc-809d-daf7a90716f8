<?php

namespace App\Http\Controllers\api;

use App\Http\Resources\CityResource;
use App\Http\Resources\DistrictResource;
use App\Http\Resources\LanguageResource;
use App\Http\Resources\ServiceResource;
use App\Http\Resources\SkillResource;
use App\Models\City;
use App\Models\District;
use App\Models\Language;
use App\Models\Service;
use App\Models\Skill;

class DataController extends ApiController
{
    public function getCities()
    {
        $cities = City::where('active', 1)->get();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => CityResource::collection($cities),
        ], 200);
    }

    public function getCitiesByDistrictId($districtId = null)
    {
        if ($districtId) {
            $cities = City::where('active', 1)->where('district_id', $districtId)->get();
        } elseif (request()->district && request()->district != '' && !empty(request()->district)) {
            $cities = City::where('active', 1)->where('district_id', request()->district)->get();
        } else {
            $cities = City::where('active', 1)->get();
        }

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => CityResource::collection($cities),
        ], 200);
    }

    public function getDistricts()
    {
        $districts = District::where('active', 1)->get();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => DistrictResource::collection($districts),
        ], 200);
    }

    public function getDistrictsByCityId($cityId)
    {
        $districts = District::where('active', 1)
            ->where('city_id', $cityId)
            ->get();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => DistrictResource::collection($districts),
        ], 200);
    }

    public function getServices()
    {
        $services = Service::where('is_active', 1)->get();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => ServiceResource::collection($services),
        ], 200);
    }

    public function getServiceById($id)
    {
        $service = Service::where('is_active', 1)->find($id);

        if (!$service) {
            return $this->sendError(__('response.Service Not Found'));
        }

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => new ServiceResource($service),
        ], 200);
    }

    public function getSkills()
    {
        $skills = Skill::get();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => SkillResource::collection($skills),
        ], 200);
    }

    public function getLanguages()
    {
        $languages = Language::where('is_active', 1)->orderBy('is_default', 'desc')->orderBy('id')->get();
        foreach ($languages as $language) {
            if (!file_exists(base_path('resources/lang/'.$language->short.'/response.php'))) {
                if (!is_dir(base_path('resources/lang/'.$language->short))) {
                    mkdir(base_path('resources/lang/'.$language->short));
                }
                copy(base_path('resources/lang/en/response.php'), base_path('resources/lang/'.$language->short.'/response.php'));
            }
            $lang_array = include base_path('resources/lang/'.$language->short.'/response.php');
            if (count($lang_array) == 0) {
                $lang_array = include base_path('resources/lang/en/response.php');
            }
            $keys = json_encode($lang_array, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            file_put_contents(base_path('storage/app/public/languages/'.$language->short.'.json'), $keys);
        }
        $languages = Language::where('is_active', 1)->orderBy('is_default', 'desc')->orderBy('id')->get();
        $languages = LanguageResource::collection($languages);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => $languages,
        ], 200);
    }
}
