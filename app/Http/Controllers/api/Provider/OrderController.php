<?php

namespace App\Http\Controllers\api\Provider;

use App\Http\Controllers\api\ApiController;
use App\Http\Requests\api\Orders\UpdateOrderPricingRequest;
use App\Http\Requests\api\Orders\UpdateOrderRequest;
use App\Http\Requests\api\Orders\UpdateOrderTrackingRequest;
use App\Http\Resources\OrderResource;
use App\Models\Configuration;
use App\Models\Earning;
use App\Models\Offer;
use App\Models\Order;
use App\Models\OrderEstimation;
use App\Models\OrderSubCategory;
use App\Models\OrderTracking;
use App\Models\PaymentTransaction;
use App\Models\Service;
use App\Models\Wallet;
use App\Notifications\Order\ClothOrderDetailUpdatedByProviderNotification;
use App\Notifications\Order\ProviderApproveBookingNotification;
use App\Notifications\Order\ProviderCancelBookingNotification;
use App\Notifications\Order\ProviderFirstApprovedNotification;
use App\Notifications\Order\ProviderNewEstimationNotification;
use App\Services\CalculatePriceService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class OrderController extends ApiController
{
    public function index()
    {
        $orders = Order::where('provider_id', Auth::user()->id)
            ->orderBy('created_at', 'desc')->get();
        $orders = OrderResource::collection($orders);

        return response()->json([
            'code' => 200, 'success' => true,
            'message' => '',
            'data' => $orders,
        ]);
    }

    public function getOrders()
    {
        $donOrders = Order::where('provider_id', Auth::user()->id)
            ->where('paid_to_provider', 1)
            ->where('p_paid_at', '>', Carbon::now()->subMonths(1))
            ->where('status', 'completed')
            ->orderBy('created_at', 'desc')->get();

        $completedOrders = Order::where('provider_id', Auth::user()->id)
            ->where('.paid_to_provider', 0)
            ->where('.status', 'completed')
            ->orderBy('.created_at', 'desc')->get();

        $orders['done'] = OrderResource::collection($donOrders);
        $orders['completed'] = OrderResource::collection($completedOrders);

        return response()->json([
            'code' => 200, 'success' => true,
            'message' => '',
            'data' => $orders,
        ]);
    }

    public function show($id)
    {
        $order = Order::where('provider_id', Auth::user()->id)
            ->where('id', $id)
            ->first();

        if (!$order) {
            return $this->sendError(__('response.This Booking Not Found In Your Bookings'));
        }
        $order = new OrderResource($order);

        return response()->json([
            'code' => 200, 'success' => true,
            'message' => '',
            'data' => $order,
        ]);
    }

    public function getOrderGroup($group)
    {
        $orders = Order::where('provider_id', Auth::user()->id)->where('group', $group)->get();
        $orders = OrderResource::collection($orders);

        return response()->json([
            'code' => 200, 'success' => true,
            'message' => '',
            'data' => $orders,
        ]);
    }

    // get order by status
    public function getOrderBySatus($status)
    {
        $orders = Order::where('provider_id', Auth::user()->id)->where('status', $status)->get();
        $orders = OrderResource::collection($orders);

        return response()->json([
            'code' => 200, 'success' => true,
            'message' => '',
            'data' => $orders,
        ]);
    }

    // get order by status
    public function getOrderByGroup($group)
    {
        $orders = Order::where('provider_id', Auth::user()->id)->where('group', $group)->get();
        $orders = OrderResource::collection($orders);

        return response()->json([
            'code' => 200, 'success' => true,
            'message' => '',
            'data' => $orders,
        ]);
    }

    // getOrderInfoForUpdate
    public function getOrderOptionsForUpdate($id)
    {
        $orderOption = OrderSubCategory::where('order_id', $id)->get();

        return response()->json([
            'code' => 200, 'success' => true,
            'message' => '',
            'data' => $orderOption,
        ]);
    }

    public function orderAction()
    {
        $orders = Order::where('provider_id', Auth::user()->id)
            ->whereIn('status', ['pending', 'received', 'delivered'])
            ->get();
        // ->whereDate('start_date', '>=', now())

        $orders = OrderResource::collection($orders);

        return response()->json([
            'code' => 200, 'success' => true,
            'message' => '',
            'data' => $orders,
        ]);
    }

    public function toDo()
    {
        $orders = Order::where('provider_id', Auth::user()->id)
            ->where('status', 'confirmed')
            ->whereDate('start_date', '=', now())->get();
        $orders = OrderResource::collection($orders);

        return response()->json([
            'code' => 200, 'success' => true,
            'message' => '',
            'data' => $orders,
        ]);
    }

    /**** Order Actions *****/

    public function cancelOrder(Request $request, $id)
    {
        $order = Order::where('provider_id', Auth::user()->id)->where('id', $id)->first();
        if (!$order) {
            return $this->sendError(__('response.This Booking Not Found In Your Bookings'));
        }
        if ($order->status == 'pending' || $order->status == 'approved') {
            $order->status = 'canceled';
            $order->canceled_by = 'provider';
            $order->save();
            $order->user->notify(new ProviderCancelBookingNotification($order));  // todo
            $order = new OrderResource($order);

            return response()->json([
                'code' => 200, 'success' => true,
                'message' => __('response.Order Has Been Canceled'),
                'data' => $order,
            ]);
        } else {
            return $this->sendError(__('response.This Order Cannot Be Canceled'));
        }
    }

    public function approveOrder(Request $request, $id)
    {
        $order = Order::where('provider_id', Auth::user()->id)
            ->where('id', $id)
            ->first();

        if (!$order) {
            return $this->sendError(__('response.This Booking Not Found In Your Bookings'));
        }

        $newOrderFinish = Carbon::parse($order->start_date)->addHours($request->duration);

        if ($order->status == 'pending' || $order->status == 'received' || $order->status == 'waiting_approval') {
            if ($order->typable && $order->typable->category_id == 4) {
                if ($request->confirm_anyway == 'false') {
                    $cross_orders = Order::where('id', '!=', $id)
                        ->where('provider_id', Auth::user()->id)
                        ->where(function ($query) use ($order) {
                            $query->where('group', '!=', $order->group)
                                  ->orWhereNull('group');
                        })
                        ->whereIn('status', ['approved', 'in_progress'])
                        ->where('start_date', '<=', $order->start_date)
                        ->where('end_date', '>=', $order->start_date)
                        ->orderBy('id', 'desc')
                        ->first();

                    if ($cross_orders) {
                        return $this->sendError(__('response.There is a conflect with another order'));
                    }
                }
            }

            $order->status = 'approved';
            // $order->end_date = $newOrderFinish;
            if ($request->has('note')) {
                $order->provider_note = $request->note;
            }
            if ($order->provider_id == null) {
                $order->provider_id = Auth::user()->id;
            }
            $order->save();

            // new code for approve multi orders
            if ($order->group != null) {
                $multiOrders = Order::where('id', '!=', $id)->where('group', $order->group)->get();
                foreach ($multiOrders as $multiOrder) {
                    if ($multiOrder->status == 'pending') {
                        $newMultiOrderFinish = Carbon::parse($multiOrder->start_date)->addHours($request->duration);
                        if ($multiOrder->service && $multiOrder->service->category_id == 4) {
                            if (!$request->confirm_anyway || $request->confirm_anyway == 'false') {
                                $cross_orders = Order::where('id', '!=', $id)
                                    ->where('provider_id', Auth::user()->id)
                                    ->where(function ($query) use ($order) {
                                        $query->where('group', '!=', $order->group)
                                              ->orWhereNull('group');
                                    })
                                    ->whereIn('status', ['approved', 'in_progress'])
                                    ->whereBetween('start_date', [$multiOrder->start_date, $newMultiOrderFinish])
                                    ->first();
                                if ($cross_orders) {
                                    return $this->sendError(__('response.There is a conflect with another order'));
                                }
                            }
                        }

                        $multiOrder->status = 'approved';
                        // $multiOrder->end_date = $newMultiOrderFinish;
                        // $multiOrder->with_deposit = 1;
                        if ($request->has('note')) {
                            $multiOrder->provider_note = $request->note;
                        }
                        if ($multiOrder->provider_id == null) {
                            $multiOrder->provider_id = Auth::user()->id;
                        }
                        $multiOrder->save();
                        $multiOrder->user->notify(new ProviderApproveBookingNotification($multiOrder));
                    }
                }
            }
            // end new code for multi approve orders

            $order->user->notify(new ProviderApproveBookingNotification($order));
            $order = new OrderResource($order);

            return response()->json([
                'code' => 200, 'success' => true,
                'message' => __('response.Order Has Been Approved'),
                'data' => $order,
            ]);
        } else {
            return $this->sendError(__('response.This Order Is '.$order->status));
        }
    }

    public function completeOrder(Request $request, $id)
    {
        $order = Order::where('provider_id', Auth::user()->id)->where('id', $id)->first();
        if (!$order) {
            return $this->sendError(__('response.This Booking Not Found In Your Bookings'));
        }
        if ($order->status == 'confirmed' || $order->status == 'delivered') {
            $paidPaymentTransaction = PaymentTransaction::where('order_id', $id)
                ->where('transaction_for', 'total_amount')
                ->where('confirmed', 1)
                ->first();
            $paidDepositTransaction = PaymentTransaction::where('order_id', $id)
                ->where('transaction_for', 'deposit')
                ->where('confirmed', 1)
                ->first();

            // if ($order->payment_left > 0) {
            //     return $this->sendError(__('response.User must pay total amount of order before change order status to done'));
            // }
            $paidTransaction = PaymentTransaction::where('order_id', $id)->where('confirmed', 1)->sum('amount');

            Log::info(number_format($paidTransaction, 2));
            Log::info(number_format($order->total_payment, 2));
            if (number_format($order->total_payment, 2) > number_format($paidTransaction, 2)) {
                return $this->sendError(__('response.User must pay total amount of order before change order status to done'));
            }
            /*  if (
                ($paidPaymentTransaction !== null && $paidPaymentTransaction->amount +  $paidDepositTransaction->amount < ($order->total_payment + $order->tips->sum('amount')))
                || ($paidPaymentTransaction === null &&  $paidDepositTransaction->amount < ($order->total_payment + $order->tips->sum('amount')))
            ) {
                return response()->json(['status' => false, 'code' => 200, 'message' => TranslationManager::translate('User must pay total amount of order before change order status to done')]);
            } */
            $order->status = 'completed';
            $order->save();

            $paidPaymentTransactions = PaymentTransaction::where('order_id', $id)
                ->where('confirmed', 1)->get();
            foreach ($paidPaymentTransactions as $transaction) {
                if ($transaction->transaction_for == 'deposit') {
                    $wallet = new Wallet();
                    $wallet->user_id = $order->provider_id;
                    $wallet->amount = $transaction->amount;
                    $wallet->transaction_id = $transaction->id;
                    $wallet->amount_type = 'debt';
                    $wallet->process_type = 'deposit';
                    $wallet->save();

                    if ($order->provider->id != null) {
                        $order->provider->wallet_balance += $wallet->amount;
                        $order->save();
                    }
                } elseif ($transaction->transaction_for == 'total_amount') {
                    $commission_configuration = Configuration::where('key', 'commission')->first();
                    if ($order === null) {
                        abort('404');
                    }
                    if ($order->provider?->providerInfo?->commission == null) {
                        $order->commission_percentage = $commission_configuration->value;
                    } else {
                        $order->commission_percentage = $order->provider?->providerInfo?->commission;
                    }
                    $order->commission_amount = ($order->commission_percentage * $order->total_payment) / 100;
                    $order->payment_left = 0;
                    $order->save();

                    $wallet = new Wallet();
                    $wallet->user_id = $order->provider_id;
                    $wallet->amount = $order->total_payment - $order->commission_amount - $order->deposit_amount - $order->tax_amount;
                    $wallet->transaction_id = $transaction->id;
                    $wallet->amount_type = 'debt';
                    $wallet->process_type = 'total_amount';
                    $wallet->save();

                    if ($order->tips->sum('amount') > 0) {
                        $wallet = new Wallet();
                        $wallet->user_id = $order->provider_id;
                        $wallet->amount = $order->tips->sum('amount');
                        $wallet->transaction_id = $transaction->id;
                        $wallet->amount_type = 'debt';
                        $wallet->process_type = 'tips';
                        $wallet->save();
                    }

                    if ($order->provider->id != null) {
                        $order->provider->wallet_balance += $wallet->amount;
                        $order->save();
                    }

                    $earning = new Earning();
                    $earning->order_id = $order->id;
                    $earning->amount = $order->commission_amount;
                    $earning->is_received = true;
                    $earning->save();
                }
            }
            $order = new OrderResource($order);

            return response()->json([
                'code' => 200, 'success' => true,
                'message' => __('response.Order Has Been Completed'),
                'data' => $order,
            ]);
        } else {
            return $this->sendError(__('response.This Order Is '.$order->status));
        }
    }

    public function setOrderPricing(UpdateOrderPricingRequest $request, $id)
    {
        $order = Order::where('provider_id', Auth::user()->id)->where('id', $id)
            ->where('typable_type', Service::class)
            ->first();
        if (!$order) {
            return $this->sendError(__('response.This Booking Not Found In Your Bookings'));
        }
        if ($order->typable_type == Offer::class || $order->service?->category?->has_types == 1) {
            return $this->sendError(__('response.This Order Service Has Fixed Price'));
        } elseif ($order->status == 'confirmed' || $order->status == 'approved') {
            OrderEstimation::where('order_id', $order->id)->where('status', 'pending')->update(['status' => 'canceled']);

            $estimation = new OrderEstimation();
            $estimation->order_id = $order->id;
            $estimation->quantity = $request->pricing_option;
            $estimation->status = 'pending';
            $estimation->save();

            $order->user->notify(new ProviderNewEstimationNotification($order));

            $order = new OrderResource($order);

            return response()->json([
                'code' => 200, 'success' => true,
                'message' => __('response.Request Has been sent to user'),
                'data' => $order,
            ]);
        } else {
            return $this->sendError(__('response.This Order Is '.$order->status));
        }
    }

    public function updateTrackingStatus(UpdateOrderTrackingRequest $request, $id)
    {
        $order = Order::where('provider_id', Auth::user()->id)->where('id', $id)->first();
        if (!$order) {
            return $this->sendError(__('response.This Booking Not Found In Your Bookings'));
        }
        if ($order->status == 'confirmed') {
            $tracking = OrderTracking::where('order_id', $id)->where('step_id', $request->step_id)->first();

            if ($tracking === null) {
                $tracking = new OrderTracking();
                $tracking->step_id = $request->step_id;
                $tracking->order_id = $id;
                $tracking->save();
            } else {
                $tracking->delete();
            }
            $order = new OrderResource($order);

            return response()->json([
                'code' => 200, 'success' => true,
                'message' => __('response.Order Tracking Steps Has Been Updated'),
                'data' => $order,
            ]);
        } else {
            return $this->sendError(__('response.Order Should Be Confirmed'));
        }
    }

    // first approved for cloth service
    public function approveClothOrder($id)
    {
        $order = Order::where('provider_id', Auth::user()->id)->where('id', $id)->first();
        if (!$order) {
            return $this->sendError(__('response.This Booking Not Found In Your Bookings'));
        }
        if ($order->typable?->category_id != 6) {
            return $this->sendError(__('response.Order Is Not For Cloth Service'));
        }
        $order->status = 'waiting_dropdawn';
        $order->save();

        $order->user->notify(new ProviderFirstApprovedNotification($order));   // todo
        $order = new OrderResource($order);

        return response()->json([
            'code' => 200, 'success' => true,
            'message' => __('response.Order Status Has Been Updated'),
            'data' => $order,
        ]);
    }

    // confirm reciveing cloth from customer
    public function confirmReceivingClothes($id)
    {
        $order = Order::where('provider_id', Auth::user()->id)->where('id', $id)->first();
        if (!$order) {
            return $this->sendError(__('response.This Booking Not Found In Your Bookings'));
        }
        if ($order->typable?->category_id != 6) {
            return $this->sendError(__('response.Order Is Not For Cloth Service'));
        }
        $order->status = 'received';
        $order->save();
        $order = new OrderResource($order);

        return response()->json([
            'code' => 200, 'success' => true,
            'message' => __('response.Order Status Has Been Updated'),
            'data' => $order,
        ]);
    }

    // confirm finishing cleaning and waiting to customer for pick up the cloth
    public function cleanFinished($id)
    {
        $order = Order::where('provider_id', Auth::user()->id)->where('id', $id)->first();
        if (!$order) {
            return $this->sendError(__('response.This Booking Not Found In Your Bookings'));
        }
        if ($order->typable?->category_id != 6) {
            return $this->sendError(__('response.Order Is Not For Cloth Service'));
        }
        $order->status = 'delivered';
        $order->save();

        $order = new OrderResource($order);

        return response()->json([
            'code' => 200, 'success' => true,
            'message' => __('response.Order Status Has Been Updated'),
            'data' => $order,
        ]);
    }

    // update order
    public function update(UpdateOrderRequest $request, $id)
    {
        $data = $request->validated();
        $order = Order::where('provider_id', Auth::user()->id)->where('id', $id)->first();
        if (!$order) {
            return $this->sendError(__('response.This Booking Not Found In Your Bookings'));
        }
        if ($order->typable?->category_id != 6) {
            return $this->sendError(__('response.Order Is Not For Cloth Service'));
        }

        if (count($request->sub_category) != count($request->quantity)) {
            return $this->sendError(__('response.Enter price for all sub category'));
        }

        $calculatePric = new CalculatePriceService();
        $response = $calculatePric->calculatePrice($request);

        $order->sub_payment = $response['sub_total'];
        $order->total_payment = $response['total_price'];
        // $order->price_without_materials = $response['price_without_materials'];
        $order->deposit_amount = $response['deposit'];
        $order->status = 'waiting_approval';
        $order->save();

        // delete the option amounts
        $deletedOrderSubs = OrderSubCategory::where('order_id', $order->id)->delete();

        // update the option amounts
        foreach ($request->sub_category as $index => $sub) {
            $orderSubs = new OrderSubCategory();
            $orderSubs->order_id = $order->id;
            $orderSubs->sub_category_id = $sub;
            $orderSubs->quantity = $request->quantity[$index];
            $orderSubs->save();
        }

        //  notify user of changes
        $order->user->notify(new ClothOrderDetailUpdatedByProviderNotification($order));   // todo

        $order = new OrderResource($order);

        return response()->json([
            'code' => 200, 'success' => true,
            'message' => __('response.Order Status Has Been Updated'),
            'data' => $order,
        ]);
    }
}
