<?php

namespace App\Http\Controllers\api\Provider;

use App\Http\Controllers\api\ApiController;
use App\Http\Requests\api\Service\StoreServiceRequest;
use App\Http\Requests\api\Service\UpdateServiceRequest;
use App\Http\Resources\ServicePriceResource;
use App\Http\Resources\SubCategoryResource;
use App\Models\PriceRange;
use App\Models\ProviderInfo;
use App\Models\ProviderService;
use App\Models\Service;
use App\Models\SubCategoryPrice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ServiceController extends ApiController
{
    public function index()
    {
        $user = request()->user_id;
        // $user_id = ProviderInfo::findorfail($user);
        $services = ProviderService::where('provider_id', $user)->with('SubCategoryPrice')->get();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => ServicePriceResource::collection($services),
        ], 200);
    }

    public function getServiceSubs($id)
    {
        $service = Service::find($id);
        if (!$service) {
            return $this->sendError(__('response.Service Not Found'));
        }

        if ($service->category?->has_subs) {
            $subs = $service->category?->subCategories;
        } else {
            return $this->sendError(__('response.Service Do Not Have Subs'));
        }

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => SubCategoryResource::collection($subs),
        ], 200);
    }

    public function getServicePricing($id)
    {
        $user = request()->user();
        $service = ProviderService::where('provider_id', $user->id)->where('service_id', $id)->first();
        if (!$service) {
            return $this->sendError(__('response.Service Not Found In Your Account'));
        }

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => new ServicePriceResource($service),
        ], 200);
    }

    public function store(StoreServiceRequest $request)
    {
        $user = request()->user();
        $data = $request->validated();

        $serviceCheck = $user->providerServices()->find($data['service_id']);
        if ($serviceCheck) {
            return $this->sendError(__('response.Service Already Added To Your Account'));
        }

        $service = Service::find($data['service_id']);
        if (!$service) {
            return $this->sendError(__('response.Service Not Found'));
        }
        DB::beginTransaction();
        // Create a new instance of ProviderService
        $providerService = new ProviderService();
        $providerService->service_id = $data['service_id'];
        $providerService->provider_id = $user->id;
        $providerService->material_price = $data['material_price'];
        if ($request->deliver == true) {
            $providerService->deliver = true;
        }
        if ($request->min_hours && $request->min_hours != null) {
            $providerService->min_hours = $request->min_hours;
        }
        $providerService->save();

        if (!$service->category?->has_subs && $service->category?->id != 5) {
            if (!isset($data['from'])) {
                return $this->sendError(__('response.From Feiled Is Required'));
            }
            if (!count($data['from']) > 0) {
                return $this->sendError(__('response.Please Enter one Range at least'));
            }

            if (count($data['from']) != count($data['price'])) {
                return $this->sendError(__('response.Please Enter Price For All Value'));
            }

            // Loop through the 'from' array from the request
            foreach ($data['from'] as $index => $from) {
                // Create a new instance of PriceRange for each price range and store the price value.
                $priceRange = new PriceRange();
                $priceRange->provider_service_id = $providerService->id;
                $priceRange->min_range = $from;
                $priceRange->max_range = isset($data['to']) ? $data['to'][$index] : null;
                $priceRange->price = $data['price'][$index];
                $priceRange->save();
            }
        } elseif ($service->category?->has_subs) {
            if (!isset($data['sub_category'])) {
                return $this->sendError(__('response.Sub Category Is Required'));
            }
            if (!count($data['sub_category']) > 0) {
                return $this->sendError(__('response.Please Enter one sub category at least'));
            }
            if (count($data['sub_category']) != count($data['price'])) {
                return $this->sendError(__('response.Please Enter Price For All Value'));
            }
            foreach ($data['sub_category'] as $index => $subCategory) {
                $subPrice = new SubCategoryPrice();
                $subPrice->provider_service_id = $providerService->id;
                $subPrice->sub_category_id = $subCategory;
                $subPrice->price = $data['price'][$index];
                $subPrice->save();
            }
        }
        DB::commit();

        return $this->sendSuccess(__('response.Service Added To Your Account'));
    }

    public function update(UpdateServiceRequest $request)
    {
        $user = request()->user();
        $data = $request->validated();

        $service = ProviderService::where('provider_id', $user->id)->where('service_id', $data['service_id'])->first();
        if (!$service) {
            return $this->sendError(__('response.Service Not Found In Your Account'));
        }
        if ($service->priceRange || $service->subCategoryPrice) {
            $service->priceRange()->delete();
            $service->subCategoryPrice()->delete();
        }
        $service->delete();

        $service = Service::find($data['service_id']);
        if (!$service) {
            return $this->sendError(__('response.Service Not Found'));
        }

        $providerService = new ProviderService();
        $providerService->service_id = $data['service_id'];
        $providerService->provider_id = $user->id;
        $providerService->material_price = $data['material_price'];
        if ($request->deliver == true) {
            $providerService->deliver = true;
        }
        if ($request->min_hours && $request->min_hours != null) {
            $providerService->min_hours = $request->min_hours;
        }
        $providerService->save();

        if (!$service->category?->has_subs && $service->category?->id != 5) {
            if (!isset($data['from'])) {
                return $this->sendError(__('response.From Feiled Is Required'));
            }
            if (!count($data['from']) > 0) {
                return $this->sendError(__('response.Please Enter one Range at least'));
            }

            if (count($data['from']) != count($data['price'])) {
                return $this->sendError(__('response.Please Enter Price For All Value'));
            }

            // Loop through the 'from' array from the request
            foreach ($data['from'] as $index => $from) {
                // Create a new instance of PriceRange for each price range and store the price value.
                $priceRange = new PriceRange();
                $priceRange->provider_service_id = $providerService->id;
                $priceRange->min_range = $from;
                $priceRange->max_range = $data['to'][$index];
                $priceRange->price = $data['price'][$index];
                $priceRange->save();
            }
        } elseif ($service->category?->has_subs) {
            if (!isset($data['sub_category'])) {
                return $this->sendError(__('response.Sub Category Is Required'));
            }
            if (!count($data['sub_category']) > 0) {
                return $this->sendError(__('response.Please Enter one sub category at least'));
            }
            if (count($data['sub_category']) != count($data['price'])) {
                return $this->sendError(__('response.Please Enter Price For All Value'));
            }

            foreach ($data['sub_category'] as $index => $subCategory) {
                $subPrice = new SubCategoryPrice();
                $subPrice->provider_service_id = $providerService->id;
                $subPrice->sub_category_id = $subCategory;
                $subPrice->price = $data['price'][$index];
                $subPrice->save();
            }
        }

        return $this->sendSuccess(__('response.Service Updated'));
    }

    public function destroy($id)
    {
        $user = request()->user();

        $service = ProviderService::where('provider_id', $user->id)->where('service_id', $id)->first();
        if (!$service) {
            return $this->sendError(__('response.Service Not Found In Your Account'));
        }
        if ($service->priceRange || $service->subCategoryPrice) {
            $service->priceRange()->delete();
            $service->subCategoryPrice()->delete();
        }
        $service->delete();

        return $this->sendSuccess(__('response.Service Deleted'));
    }
}
