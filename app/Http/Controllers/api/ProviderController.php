<?php

namespace App\Http\Controllers\api;

use App\Http\Requests\api\provider\SetWorkTimeRequest;
use App\Http\Resources\CityResource;
use App\Http\Resources\ProviderHolidayResource;
use App\Http\Resources\ProviderResource;
use App\Http\Resources\ProviderWorkingTimeResource;
use App\Http\Resources\ReviewResource;
use App\Models\ProviderHoliday;
use App\Models\ProviderInfo;
use App\Models\ProviderWorkTime;
use App\Models\Review;
use App\Models\Service;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProviderController extends ApiController
{
    public function getAllProviders()
    {// city_id - service_id - date - search
        if (request()->user_id) {
            $user = request()->user_id;
            $query = ProviderInfo::whereRelation('user', 'is_verified', '=', 1)
            ->where('user_id', '!=', $user)
                ->whereRelation('user', 'is_blocked', '=', 0);
            if (request()->city_id) {
            }
            $providers = $query->with('user.providerService')->get();
        } else {
            $providers = ProviderInfo::whereRelation('user', 'is_verified', '=', 1)
                ->whereRelation('user', 'is_blocked', '=', 0)
                ->get();
        }

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => ProviderResource::collection($providers),
        ], 200);
    }

    public function getProviderByUser($id)
    {
        $provider = ProviderInfo::whereRelation('user', 'is_verified', '=', 1)
            ->whereRelation('user', 'is_blocked', '=', 0)
            ->where('user_id', $id)
            ->first();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrieved'),
            'data' => new ProviderResource($provider),
        ], 200);
    }

    public function getProviderByID($id)
    {
        $provider = ProviderInfo::find($id);

        if ($provider === null) {
            return $this->sendError(__('response.Provider Not Found'));
        }

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrieved'),
            'data' => new ProviderResource($provider),
        ], 200);
    }

    public function setWorkCity(Request $request)
    {
        $id = Auth::user()->providerInfo->id;

        $provider = ProviderInfo::find($id);
        if ($provider === null) {
            return $this->sendError(__('response.Provider Not Found'));
        }

        $provider->user?->providerWorkZones()->sync($request->work_zones);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Stored'),
            'data' => CityResource::collection($provider->user?->providerWorkZones),
        ], 200);
    }

    public function getWorkCity()
    {
        $id = Auth::user()->providerInfo->id;
        $provider = ProviderInfo::find($id);
        if ($provider === null) {
            return $this->sendError(__('response.Provider Not Found'));
        }

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => CityResource::collection($provider->user?->providerWorkZones),
        ], 200);
    }

    public function setWorkTime(SetWorkTimeRequest $request)
    {
        $id = Auth::user()->providerInfo->id;

        $provider = ProviderInfo::find($id);
        if ($provider === null) {
            return $this->sendError(__('response.Provider Not Found'));
        }

        $provider->user->providerWorkTimes()->delete();

        foreach ($request->days as $index => $day) {
            $providerWorkTime = new ProviderWorkTime();
            $providerWorkTime->user_id = Auth::user()->id;
            $providerWorkTime->day = $day;
            $providerWorkTime->start_at = $request->start_at[$index];
            $providerWorkTime->end_at = $request->end_at[$index];
            $providerWorkTime->save();
        }

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Stored'),
            'data' => $provider->user?->providerWorkTimes,
        ], 200);
    }

    public function getWorkTime()
    {
        $id = Auth::user()->providerInfo->id;
        $provider = ProviderInfo::find($id);
        if ($provider === null) {
            return $this->sendError(__('response.Provider Not Found'));
        }
        $working_time = ProviderWorkingTimeResource::collection($provider->user?->providerWorkTimes);
        $holidays = ProviderHolidayResource::collection($provider->user?->providerHolidays);
        $data = [
            'working_times' => $working_time,
            'holidays' => $holidays,
        ];

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => $data,
        ], 200);
    }

    public function deleteWorkTime($id)
    {
        $working_time = ProviderWorkTime::where('user_id', Auth::user()->id)
            ->where('id', $id)->first();
        if ($working_time == null) {
            return $this->sendError('response.This Day Not Exists');
        }
        $working_time->delete();

        return $this->sendSuccess(__('response.Working Day Deleted Successfully'));
    }

    public function setHoliday(Request $request)
    {
        $request->validate([
            'starts_at' => 'required|date|after_or_equal:today',
            'ends_at' => 'required|date|after_or_equal:starts_at',
        ]);

        $user = Auth::user();

        if (!$user->providerInfo) {
            return $this->sendError(__('response.Provider Not Found'));
        }

        // Check for overlapping holidays
        $overlapping = ProviderHoliday::where('user_id', $user->id)
            ->where(function ($query) use ($request) {
                $query->whereBetween('starts_at', [$request->starts_at, $request->ends_at])
                    ->orWhereBetween('ends_at', [$request->starts_at, $request->ends_at])
                    ->orWhere(function ($q) use ($request) {
                        $q->where('starts_at', '<=', $request->starts_at)
                          ->where('ends_at', '>=', $request->ends_at);
                    });
            })
            ->exists();

        if ($overlapping) {
            return response()->json([
                'success' => false,
                'code' => 0,
                'message' => __('response.Holiday_Period_Overlaps'),
            ], 422);
        }

        $holiday = new ProviderHoliday();
        $holiday->user_id = $user->id;
        $holiday->starts_at = $request->starts_at;
        $holiday->ends_at = $request->ends_at;
        $holiday->save();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Holiday_Added_Successfully'),
            'data' => new ProviderHolidayResource($holiday),
        ], 200);
    }

    public function deleteHoliday($id)
    {
        $holiday = ProviderHoliday::where('user_id', Auth::user()->id)
            ->where('id', $id)->first();

        if ($holiday == null) {
            return $this->sendError(__('response.Holiday Not Found'));
        }

        $holiday->delete();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Holiday_Deleted_Successfully'),
        ], 200);
    }

    public function filter(Request $request)
    {
        $query = User::query();

        if ($request->has('user_id') && $request->user_id) {
            $query->where('is_verified', 1)->where('is_blocked', 0)->whereIn('type', ['provider', 'company'])
                ->where('id', '!=', $request->user_id)
                ->whereRelation('providerInfo', 'is_approved', '=', 1);
        } else {
            $query->where('is_verified', 1)->where('is_blocked', 0)->whereIn('type', ['provider', 'company'])
                ->whereRelation('providerInfo', 'is_approved', '=', 1);
        }
        if ($request->has('service_id') && $request->service_id != null) {
            $query->whereRelation('providerServices', 'service_id', '=', $request->service_id);
        }

        if ($request->has('search') && $request->search != null) {
            $query->whereRelation('providerInfo', 'name', 'LIKE', '%'.$request->search.'%');
        }

        if ($request->has('city_id') && $request->city_id != null) {
            $query->whereRelation('providerWorkZones', 'city_id', '=', $request->city_id);
        }

        if ($request->has('date') && $request->date != null && $request->date >= date('Y-m-d')) {
            $query->whereDoesntHave('providerHolidays', function ($hQuery) use ($request) {
                $hQuery->whereDate('starts_at', '<=', $request->date)->whereDate('ends_at', '>=', $request->date);
            });

            $day_name = strtolower(date('l', strtotime($request->date)));

            if ($request->has('duration')) {
                $duration = $request->duration;
            } else {
                $duration = 1;
            }

            if ($request->has('time') && $request->time != null) {
                $start_time = date('H:i', strtotime($request->date.' '.$request->time));
                $end_time = date('H:i', strtotime($request->date.' '.$request->time) + ($duration * 3600));

                // Query to filter records based on working times for a given day and time range
                $query->whereHas('providerWorkTimes', function ($subQuery) use ($day_name, $request, $end_time) {
                    // Filter records where the day matches the given day name
                    $subQuery->where('day', $day_name)
                        // and the start time is less than or equal to the requested time
                        ->where('start_at', '<=', $request->time)
                        // and the end time is greater than or equal to the end time
                        ->where('end_at', '>=', $end_time);
                });

                if ($request->has('service_id') && $request->service_id != null) {
                    $service = Service::where('id', $request->service_id)->first();
                    if ($service === null) {
                        return $this->sendError(__('response.Service Not Found'));
                    }
                    if ($service->duration == 1) {
                        $query->where(function ($mainQuery) use ($request, $start_time, $end_time) {
                            $mainQuery->where(function ($type_query) use ($request, $start_time, $end_time) {
                                $type_query->where('type', 'provider')
                                    ->whereDoesntHave('providerSchedule', function ($sub_query) use ($request, $start_time, $end_time) {
                                        $start_date_time = $request->date.' '.$start_time;
                                        $end_date_time = $request->date.' '.$end_time;
                                        $sub_query->whereBetween('start_at', [$start_date_time, $end_date_time])
                                            ->orWhereBetween('end_at', [$start_date_time, $end_date_time])
                                            ->orWhere(function ($time_query) use ($start_date_time, $end_date_time) {
                                                $time_query->where('start_at', '<=', $start_date_time)
                                                    ->where('end_at', '>=', $end_date_time);
                                            });
                                    });
                            })
                                ->orWhere('type', 'company');
                        });
                    }
                }
            } else {
                $query->whereRelation('providerWorkTimes', 'day', '=', $day_name);
            }
        }

        $users = $query->with('providerInfo', 'providerService', 'albums', 'albums.photo')->get();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('message.Data_Retrieved'),
            'data' => ProviderResource::collection($users->pluck('providerInfo')),
        ], 200);
    }

    public function indexReview(Request $request)
    {
        $reveiws = Review::where('provider_id', Auth::user()->id)->where('is_active', 1)->with(['order'])->latest()->get();
        $reveiws = ReviewResource::collection($reveiws);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrieved'),
            'data' => $reveiws,
        ], 200);
    }
}
