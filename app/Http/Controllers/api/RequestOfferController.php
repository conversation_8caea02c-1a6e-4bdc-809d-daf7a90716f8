<?php

namespace App\Http\Controllers\api;

use App\Http\Requests\api\RequestOffer\ApprovedRequestOffersRequest;
use App\Http\Requests\api\RequestOffer\SaveBookingDateRequest;
use App\Http\Requests\api\RequestOffer\StoreRquestOfferRequest;
use App\Http\Resources\OrderResource;
use App\Models\Order;
use App\Models\Service;
use App\Models\User;
use App\Notifications\CancelRequestOfferNotification;
use App\Notifications\NewRequestOfferNotification;
use App\Notifications\UnPaidRequestOfferNotification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class RequestOfferController extends ApiController
{
    // store new RequestOffer recored
    public function store(StoreRquestOfferRequest $request)
    {
        $data = $request->validated();

        if (Auth::user()->id == $data['provider_id']) {
            return $this->sendError(__('response.You can not send offer request to your self'));
        }

        DB::beginTransaction();

        $newRequest = new Order();
        $newRequest->user_id = Auth::user()->id;
        $newRequest->fill($data);

        // تحقق من وجود الخدمة وأنها من الفئة المناسبة
        try {
            $service = Service::findOrFail($request->service_id);
            $newRequest->typable_type = Service::class;
            $newRequest->typable_id = $service->id;

            // إذا كانت الخدمة ليست من الفئة 5 أو لا توجد فئة، نسمح بالطلب
            // if ($service->category_id != 5) {
            //     DB::rollBack();
            //     return $this->sendError(__('response.You Can Not Request Offer For This Offer'));
            // }
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->sendError(__('response.Service not found'));
        }

        $newRequest->save();
        try {
            $provider = User::findOrFail($data['provider_id']);
            $provider->notify(new NewRequestOfferNotification($newRequest));
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->sendError(__('response.Error sending notification to provider'));
        }

        DB::commit();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Your Rquest Sent to provider'),
            'data' => new OrderResource($newRequest),
        ], 200);
    }

    public function getUserPendingOffers()
    {
        $pendingRequest = Order::where('user_id', Auth::user()->id)
            ->whereHasMorph('typable', 'App\Models\Service', function ($innerQuery) {
                $innerQuery->where('category_id', 5);
            })
            ->where('status', 'pending')
            ->where('booked_at', null)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Offer request retrived successfully'),
            'data' => OrderResource::collection($pendingRequest),
        ], 200);
    }

    // get the waiting requested offers
    public function getUserWaitingRequestedOffers()
    {
        $waitingRequest = Order::where('user_id', Auth::user()->id)
            ->whereHasMorph('typable', 'App\Models\Service', function ($innerQuery) {
                $innerQuery->where('category_id', 5);
            })
            ->where('status', 'pending')
            ->where('booked_at', '!=', null)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Offer request retrived successfully'),
            'data' => OrderResource::collection($waitingRequest),
        ], 200);
    }

    // unPaid offer request
    public function getUserUnPaidOffers()
    {
        $paidRequest = Order::where('user_id', Auth::user()->id)
            ->whereHasMorph('typable', 'App\Models\Service', function ($innerQuery) {
                $innerQuery->where('category_id', 5);
            })
            ->where('status', 'approved')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Offer request retrived successfully'),
            'data' => OrderResource::collection($paidRequest),
        ], 200);
    }

    /****** provider function ******/

    // pending offer request
    public function getProviderPendingOffers()
    {
        $pendingRequest = Order::where('provider_id', Auth::user()->id)
            ->whereHasMorph('typable', 'App\Models\Service', function ($innerQuery) {
                $innerQuery->where('category_id', 5);
            })
            ->where('status', 'pending')
            ->where('booked_at', null)->orderBy('created_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Offer request retrived successfully'),
            'data' => OrderResource::collection($pendingRequest),
        ], 200);
    }

    // save booking date
    public function saveBookingDate(SaveBookingDateRequest $request)
    {
        $data = $request->validated();
        $requestedOffer = Order::findOrFail($data['order_id']);
        $requestedOffer->update(['booked_at' => $data['booked_at']]);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Offer request retrived successfully'),
            'data' => new OrderResource($requestedOffer),
        ], 200);
    }

    // get the waiting requested offers
    public function getWaitingRequestedOffers()
    {
        $waitingRequest = Order::where('provider_id', Auth::user()->id)
            ->whereHasMorph('typable', 'App\Models\Service', function ($innerQuery) {
                $innerQuery->where('category_id', 5);
            })
            ->where('status', 'pending')
            ->where('booked_at', '!=', null)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Offer request retrived successfully'),
            'data' => OrderResource::collection($waitingRequest),
        ], 200);
    }

    // get the waiting requested offers
    public function getUnPaidRequestedOffers()
    {
        $waitingRequest = Order::where('provider_id', Auth::user()->id)
            ->whereHasMorph('typable', 'App\Models\Service', function ($innerQuery) {
                $innerQuery->where('category_id', 5);
            })
            ->where('status', 'approved')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Offer request retrived successfully'),
            'data' => OrderResource::collection($waitingRequest),
        ], 200);
    }

    // approve the requested offer
    public function approveOffers(ApprovedRequestOffersRequest $request)
    {
        $data = $request->validated();
        $requestedOffer = Order::findOrFail($data['order_id']);
        if ($requestedOffer->provider_id != Auth::user()->id) {
            return $this->sendError(__('response.You can not approve this offer request'));
        }

        DB::beginTransaction();
        $requestedOffer->update($data);
        $requestedOffer->status = 'approved';
        $requestedOffer->total_payment = $data['total_payment'];
        $requestedOffer->deposit_amount = $data['total_payment'];
        $requestedOffer->payment_left = $data['total_payment'];
        $requestedOffer->save();

        try {
            $user = User::findOrFail($requestedOffer->user->id);
            $user->notify(new UnPaidRequestOfferNotification($requestedOffer));
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->sendError(__('response.Error sending notification to user'));
        }
        DB::commit();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Offer request retrived successfully'),
            'data' => new OrderResource($requestedOffer),
        ], 200);
    }

    public function cancelOffers($id)
    {
        $requestedOffer = Order::findOrFail($id);
        if ($requestedOffer->provider_id != Auth::user()->id) {
            return $this->sendError(__('response.You can not approve this offer request'));
        }

        DB::beginTransaction();
        $requestedOffer->status = 'canceled';
        $requestedOffer->save();
        try {
            $user = User::findOrFail($requestedOffer->user->id);
            $user->notify(new CancelRequestOfferNotification($requestedOffer));
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->sendError(__('response.Error sending notification to user'));
        }
        DB::commit();

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Offer request retrived successfully'),
            'data' => new OrderResource($requestedOffer),
        ], 200);
    }
}
