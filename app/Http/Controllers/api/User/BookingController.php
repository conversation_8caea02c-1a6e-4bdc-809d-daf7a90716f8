<?php

namespace App\Http\Controllers\api\User;

use App\Http\Controllers\api\ApiController;
use App\Http\Requests\api\Booking\BookingRequest;
use App\Http\Requests\api\Booking\PricingRequest;
use App\Http\Resources\OrderResource;
use App\Models\JobApplication;
use App\Models\Order;
use App\Models\OrderSubCategory;
use App\Models\Service;
use App\Models\Task;
use App\Models\User;
use App\Services\CalculatePriceService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class BookingController extends ApiController
{
    // public function requestTime(Request $request)
    // {
    //     if (!$request->provider_id) {
    //         return $this->sendError(__('response.Provider ID is required'));
    //     }
    //     $provider = User::where('is_verified', 1)->where('is_blocked', 0)->whereIn('type', ['provider', 'company'])
    //         ->find($request->provider_id);
    //     if (!$provider) {
    //         return $this->sendError(__('response.Provider Not Found'));
    //     }
    //     if ($request->has('address_id')) {
    //         if (!$provider->providerWorkZones()->find($request->address_id)) {
    //             return $this->sendError(__('response.Provider Do Not Work In This Area'));
    //         }
    //     }

    //     $service = null;
    //     if ($request->has('service_id')) {
    //         $service = $provider->providerServices()->find($request->service_id);
    //         if (!$service) {
    //             return $this->sendError(__('response.Provider Do Not Offer This Service'));
    //         }
    //     }

    //     $booking_days = [];
    //     $start_time = date('Y-m-d', strtotime('+ 1 Day'));
    //     $end_time = date('Y-m-d', strtotime('+ 1 Month'));
    //     if ($request->has('date')) {
    //         $start_time = $request->date;
    //         $end_time = $request->date;
    //     }
    //     foreach (range(strtotime($start_time), strtotime($end_time), 24 * 3600) as $date_time) {
    //         $booking_times = [];
    //         $date_fullname = strtolower(date('l', $date_time));
    //         $date = date('Y-m-d', $date_time);

    //         $working_time = $provider->providerWorkTimes()
    //             ->where('day', $date_fullname)
    //             ->first();

    //         $holiday = $provider->providerHolidays()
    //             ->where('starts_at', '<=', $date)
    //             ->where('ends_at', '>=', $date)
    //             ->first();

    //         if ($working_time && !$holiday) {
    //             $booking_times = [];

    //             $workStartTime = Carbon::createFromFormat('Y-m-d H:i:s', $date.' '.$working_time->start_at); // Create Carbon object for start time
    //             $workEndsTime = Carbon::createFromFormat('Y-m-d H:i:s', $date.' '.$working_time->end_at); // Create Carbon object for end time

    //             $duration = ($request->has('duration') && $service->category?->id == 1 ? $request->duration : null);
    //             $duration = max($duration, 0.5); // Ensure duration is at least 0.5

    //             for ($x = $workStartTime->timestamp; $x <= ($workEndsTime->timestamp - ($duration * 3600)); $x += ($duration * 3600)) {
    //                 if ($x > Carbon::now()->timestamp) {
    //                     $booking_times[] = Carbon::createFromTimestamp($x)->format('h:i A');
    //                 }
    //             }
    //             if (!empty($booking_times)) {
    //                 $booking_days[] = [
    //                     'date' => $date,
    //                     'date_details' => [
    //                         'day_number' => date('d', $date_time),
    //                         'day_name' => date('D', $date_time),
    //                     ],
    //                     'booking_times' => $booking_times,
    //                 ];
    //             }
    //         }
    //     }

    //     return response()->json([
    //         'code' => 200, 'success' => true, 'data' => [
    //             'booking_days' => $booking_days,
    //         ],
    //     ]);
    // }
    public function requestTime(Request $request)
    {
        if (!$request->provider_id) {
            return $this->sendError(__('response.Provider ID is required'));
        }
        $provider = User::where('is_verified', 1)->where('is_blocked', 0)->whereIn('type', ['provider', 'company'])
            ->find($request->provider_id);
        if (!$provider) {
            return $this->sendError(__('response.Provider Not Found'));
        }
        if ($request->has('address_id')) {
            if (!$provider->providerWorkZones()->find($request->address_id)) {
                return $this->sendError(__('response.Provider Do Not Work In This Area'));
            }
        }

        $service = null;
        if ($request->has('service_id')) {
            $service = $provider->providerServices()->find($request->service_id);
            if (!$service) {
                return $this->sendError(__('response.Provider Do Not Offer This Service'));
            }
        }

        $booking_days = [];
        $start_time = date('Y-m-d', strtotime('+ 1 Day'));
        $end_time = date('Y-m-d', strtotime('+ 1 Month'));
        if ($request->has('date')) {
            $start_time = $request->date;
            $end_time = $request->date;
        }

        // جلب كل job_applications المتكررة لهذا المزود
        $jobApps = JobApplication::where('provider_id', $request->provider_id)
            ->where('is_active', true)
            ->get();

        // جلب كل المهام المنفذة لهذا المزود في الفترة المحددة
        $tasks = Task::where('provider_id', $request->provider_id)
            ->whereBetween('execution_date', [$start_time, $end_time])
            ->get();

        // جلب كل الحجوزات (orders) لهذا المزود في الفترة المحددة
        $orders = Order::where('provider_id', $request->provider_id)
            ->whereBetween('start_date', [$start_time, $end_time])
            ->get();

        foreach (range(strtotime($start_time), strtotime($end_time), 24 * 3600) as $date_time) {
            $booking_times = [];
            $date_fullname = strtolower(date('l', $date_time));
            $date = date('Y-m-d', $date_time);

            $working_time = $provider->providerWorkTimes()
                ->where('day', $date_fullname)
                ->first();

            $holiday = $provider->providerHolidays()
                ->where('starts_at', '<=', $date)
                ->where('ends_at', '>=', $date)
                ->first();

            if ($working_time && !$holiday) {
                $booking_times = [];

                $workStartTime = Carbon::createFromFormat('Y-m-d H:i:s', $date.' '.$working_time->start_at);
                $workEndsTime = Carbon::createFromFormat('Y-m-d H:i:s', $date.' '.$working_time->end_at);

                $duration = ($request->has('duration') && $service->category?->id == 1 ? $request->duration : null);
                $duration = max($duration, 0.5);

                // إنشاء قائمة بالأوقات المحجوزة من job applications
                $bookedTimes = [];

                // التحقق من job applications المتكررة
                foreach ($jobApps as $jobApp) {
                    $createdAt = $jobApp->created_at->copy()->startOfDay();
                    $currentDate = Carbon::createFromFormat('Y-m-d', $date);

                    if ($currentDate->lt($createdAt)) {
                        continue; // لا نتحقق من الأيام قبل تاريخ إنشاء الجوب
                    }

                    $schedule = $jobApp->schedule ?? [];
                    foreach ($schedule as $item) {
                        $dayName = strtolower($item['day']);
                        if ($dayName === $date_fullname) {
                            foreach ($item['hours'] as $hour) {
                                $dId = $hour['d_id'] ?? null;
                                $startTime = $hour['start_time'];
                                $jobDuration = (int) $hour['duration'];
                                $endTime = Carbon::parse($startTime)->addHours($jobDuration)->format('H:i');

                                // التحقق من وجود task منفذ لهذا اليوم
                                $task = $tasks->first(function ($t) use ($jobApp, $dId, $date) {
                                    return $t->job_application_id == $jobApp->id
                                           && $t->d_id == $dId
                                           && $t->execution_date == $date;
                                });

                                // إذا لم يكن هناك task منفذ، أو كان الـ task مبدوء وغير منتهي، فالوقت محجوز
                                if (!$task || ($task->start_time && !$task->end_time)) {
                                    $bookedTimes[] = [
                                        'start' => $startTime,
                                        'end' => $endTime,
                                    ];
                                }
                            }
                        }
                    }
                }

                // التحقق من orders
                foreach ($orders as $order) {
                    if ($order->start_date === $date) {
                        $orderStart = $order->start_time ?? '00:00';
                        $orderEnd = $order->end_time ?? '23:59';
                        $bookedTimes[] = [
                            'start' => $orderStart,
                            'end' => $orderEnd,
                        ];
                    }
                }

                // توليد الأوقات المتاحة مع التحقق من التداخل
                for ($x = $workStartTime->timestamp; $x <= ($workEndsTime->timestamp - ($duration * 3600)); $x += ($duration * 3600)) {
                    if ($x > Carbon::now()->timestamp) {
                        $timeSlot = Carbon::createFromTimestamp($x)->format('H:i');
                        $timeSlotEnd = Carbon::createFromTimestamp($x + ($duration * 3600))->format('H:i');

                        // التحقق من عدم تداخل هذا الوقت مع الأوقات المحجوزة
                        $isAvailable = true;
                        foreach ($bookedTimes as $booked) {
                            if ($this->timesOverlap($timeSlot, $timeSlotEnd, $booked['start'], $booked['end'])) {
                                $isAvailable = false;
                                break;
                            }
                        }

                        if ($isAvailable) {
                            $booking_times[] = Carbon::createFromTimestamp($x)->format('h:i A');
                        }
                    }
                }

                if (!empty($booking_times)) {
                    $booking_days[] = [
                        'date' => $date,
                        'date_details' => [
                            'day_number' => date('d', $date_time),
                            'day_name' => date('D', $date_time),
                        ],
                        'booking_times' => $booking_times,
                    ];
                }
            }
        }

        return response()->json([
            'code' => 200,
            'success' => true,
            'data' => [
                'booking_days' => $booking_days,
            ],
        ]);
    }

    /**
     * التحقق من تداخل الأوقات.
     */
    private function timesOverlap($start1, $end1, $start2, $end2)
    {
        $start1 = Carbon::createFromFormat('H:i', $start1);
        $end1 = Carbon::createFromFormat('H:i', $end1);
        $start2 = Carbon::createFromFormat('H:i', $start2);
        $end2 = Carbon::createFromFormat('H:i', $end2);

        return $start1->lt($end2) && $start2->lt($end1);
    }

    public function requestPricing(PricingRequest $request)
    {
        $calculatePric = new CalculatePriceService();
        $vlidator = $calculatePric->validate($request);

        if ($vlidator->original['code'] == -1) {
            return $vlidator;
        }
        $response = $calculatePric->calculatePrice($request);

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Data_Retrived'),
            'data' => $response,
        ], 200);
    }

    public function generateUniqueCode()
    {
        $numbers = Str::random(9);
        $char = Str::random(1, 'abcdefghijklmnopqrstuvwxyz');
        $code = $numbers.$char;
        while (Order::where('group', $code)->exists()) {
            $numbers = Str::random(9);
            $char = Str::random(1, 'abcdefghijklmnopqrstuvwxyz');
            $code = $numbers.$char;
        }

        return $code;
    }

    public function requestBooking(BookingRequest $request)
    {
        // gererate group code for multi orders
        if (count($request->date) > 1) {
            $groupCode = $this->generateUniqueCode();
        } else {
            $groupCode = null;
        }

        // validate request
        $calculatePric = new CalculatePriceService();
        $vlidator = $calculatePric->validate($request);

        if ($vlidator->original['code'] == -1) {
            return $vlidator;
        }

        $service = Service::where('id', $request->service_id)->where('is_active', 1)->first();

        $multiDates = $request->date;
        $multiTimes = $request->time;
        $subCategories = $request->sub_category;

        // create order for each date
        foreach ($multiDates as $index => $multiDate) {
            if ($service->category_id == 1) {
                $response = $calculatePric->calculatePrice($request, true, $index);
            } else {
                $response = $calculatePric->calculatePrice($request);
            }

            $order = new Order();
            $order->user_id = auth()->user()->id;
            $order->provider_id = $request->provider_id;
            $order->group = $groupCode;
            $order->typable_type = Service::class;
            $order->typable_id = $request->service_id;
            $order->city_id = $request->city_id;
            $order->quantity = $request->quantity[$index];
            $order->status = 'pending';
            $order->sub_payment = $response['sub_total'];
            $order->total_payment = $response['total_price'];
            $order->deposit_amount = $response['deposit'];
            $order->payment_left = $response['total_price'] - $response['deposit'];
            $order->material_price = $response['materials_price'];
            $order->tax_amount = $response['tax'];
            $order->tax_percentage = $response['tax_percentage'];
            $order->commission_amount = $response['commission'];
            $order->commission_percentage = $response['commission_percentage'];
            $order->discount_amount = $response['discount_amount'];
            $order->discount_percentage = $response['discount_percentage'];
            $order->booked_at = date('Y-m-d H:i:s');
            $order->user_note = ($request->has('note')) ? $request->note : null;
            $order->user_lat = $request->user_lat;
            $order->user_long = $request->user_long;
            $order->address = $request->address;
            $order->is_urgent = $request->is_urgent ?? 0;
            $order->start_date = date('Y-m-d H:i:s', strtotime($multiDate.' '.date('H:i:s', strtotime($multiTimes[$index]))));
            $order->end_date = date('Y-m-d H:i:s', strtotime($multiDate.' '.date('H:i:s', strtotime($multiTimes[$index]) + $request->quantity[$index] * 3600)));
            // add coupon id isset
            if ($response['coupon_id'] != null) {
                $order->coupon_id = $response['coupon_id'];
                $order->coupon_total = $response['coupon_amount'];
            }
            $order->save();
        }

        if ($service->category?->has_subs == 1) {
            foreach ($subCategories as $subIndex => $subCategory) {
                $orderSubs = new OrderSubCategory();
                $orderSubs->order_id = $order->id;
                $orderSubs->sub_category_id = $subCategory;
                $orderSubs->quantity = $request->quantity[$subIndex];
                $orderSubs->save();
            }
        }

        if ($groupCode) {
            $orders = Order::where('group', $groupCode)->get();
            $order = OrderResource::collection($orders);
        } else {
            $order = new OrderResource($order);
        }

        return response()->json([
            'success' => true,
            'code' => 1,
            'message' => __('response.Order Sent Successfully To Provider'),
            'data' => $order,
        ], 200);
    }
}
