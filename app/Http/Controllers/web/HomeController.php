<?php

namespace App\Http\Controllers\web;

use App\Http\Controllers\Controller;
use App\Models\AppFeature;
use App\Models\Configuration;
use App\Models\Language;
use App\Models\Landing;
use App\Models\Slider;
use App\Models\Social;

class HomeController extends Controller
{
    public function index()
    {
        $current_locale = app()->getLocale();
        $current_language = Language::where('short', $current_locale)->first();
        if (!$current_language) {
            $current_language = Language::where('is_default', 1)->first();
        }
        $other_languages = Language::where('short', '!=', ($current_language->short ?? $current_locale))
            ->where('is_active', 1)
            ->orderBy('is_default', 'desc')
            ->get();
        
        // جلب البيانات من الجداول الموجودة
        $landing_slider = Landing::where('section', 'landing_sliders')->first() ?: 
            (object)['title' => config('app.name'), 'description' => 'منصة خدمات التنظيف والصيانة المنزلية'];
        $sliders = Slider::where('is_active', 1)->get();
        
        $about_sections = Landing::where('section', 'about')->get();
        $howitworks = Landing::where('section', 'how_its_work')->get();
        $screens = Landing::where('section', 'app_screens')->get();
        $app_features_one = AppFeature::all();
        $feature_image = Landing::where('section', 'feature_image')->first();
        
        $ios_app = Configuration::where('key', 'ios_app')->first();
        $android_app = Configuration::where('key', 'android_app')->first();
        $contact_phone = Configuration::where('key', 'phone')->first();
        $contact_email = Configuration::where('key', 'email')->first();
        $contact_address = Configuration::where('key', 'address')->first();
        $socials = Social::get();

        return view('welcome', compact([
            'current_language', 'other_languages', 'landing_slider', 'sliders', 'ios_app',
            'android_app', 'about_sections', 'screens', 'app_features_one', 'feature_image',
            'howitworks', 'contact_phone', 'contact_email', 'contact_address', 'socials'
        ]));
    }
}
