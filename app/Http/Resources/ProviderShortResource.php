<?php

namespace App\Http\Resources;

use App\Models\Configuration;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class ProviderShortResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $currentLanguage = $request->header('lang') ?? 'en';
        $response['id'] = $this->id;
        $response['name'] = $this->name;
        $response['phone'] = $this->phone;
        $response['image'] = ($this->image != '' && !empty($this->image)) ? Storage::disk('public')->url($this->image) : '';

        $response['address'] = $this->user?->address;
        $response['city'] = CityResource::make($this->user?->city);
        $response['district'] = DistrictResource::make($this->user?->city?->district);
        $response['commission'] = $this->commission ? $this->commission : intval(Configuration::where('key', 'commission')->first()->value);
        $response['tax'] = Configuration::where('key', 'tax')->first()?->value;

        return $response;
    }
}
