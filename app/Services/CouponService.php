<?php

namespace App\Services;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Order;
use App\Models\Coupon;
use Illuminate\Support\Facades\Auth;

class CouponService
{
    /**
     * Validate a coupon code.
     *
     * @param int|null $userId For testing purposes when not authenticated
     */
    public function validateCoupon(string $code, ?int $userId = null): array
    {
        // Find the coupon by code
        $coupon = Coupon::where('code', $code)->first();

        // Check if coupon exists
        if (!$coupon) {
            return [
                'valid' => false,
                'message' => __('message.coupon_not_found'),
            ];
        }

        // Check if coupon is active
        if (!$coupon->is_active) {
            return [
                'valid' => false,
                'message' => __('message.coupon_inactive'),
            ];
        }

        // Check if coupon is within valid date range
        $now = Carbon::now();
        if ($now->lt($coupon->start_date) || $now->gt($coupon->end_date)) {
            return [
                'valid' => false,
                'message' => __('message.coupon_expired'),
            ];
        }

        // Check if user has already used this coupon
        $checkUserId = $userId;

        // If no user_id provided but user is authenticated, use the authenticated user's ID
        if (!$checkUserId && Auth::check()) {
            $checkUserId = Auth::user()->id;
        }

        // For debugging purposes, log the user ID we're checking against
        \Log::info('Checking if user has already used coupon', [
            'user_id' => $checkUserId,
            'coupon_id' => $coupon->id,
            'coupon_code' => $coupon->code,
        ]);

        // If we have a user ID to check against
        if ($checkUserId) {
            // Get all orders for this user
            $userOrders = Order::where('user_id', $checkUserId)->get();
            \Log::info('All orders for user', [
                'user_id' => $checkUserId,
                'orders_count' => $userOrders->count(),
                'orders' => $userOrders->map(function ($order) {
                    return [
                        'id' => $order->id,
                        'coupon_id' => $order->coupon_id,
                    ];
                }),
            ]);
            $cleanUser = User::find($checkUserId);
            // Check if the user has already used this coupon
            $userUsedCoupon = Order::where('user_id', $cleanUser->id)
                ->where('coupon_id', $coupon->id)
                ->exists();

            \Log::info('User coupon usage check result', [
                'user_id' => $checkUserId,
                'coupon_id' => $coupon->id,
                'has_used' => $userUsedCoupon,
                'sql' => Order::where('user_id', $checkUserId)
                    ->where('coupon_id', $coupon->id)
                    ->toSql(),
            ]);

            if ($userUsedCoupon) {
                return [
                    'valid' => false,
                    'message' => __('message.coupon_already_used'),
                ];
            }

            // Double-check with a direct query
            $directQuery = "SELECT COUNT(*) as count FROM orders WHERE user_id = {$checkUserId} AND coupon_id = {$coupon->id}";
            $directResult = \DB::select($directQuery);
            $directCount = $directResult[0]->count;

            \Log::info('Direct query check result', [
                'query' => $directQuery,
                'count' => $directCount,
            ]);

            if ($directCount > 0) {
                return [
                    'valid' => false,
                    'message' => __('message.coupon_already_used'),
                ];
            }
        }
        // If no user ID is provided, we skip the check for whether this specific user has used the coupon
        // This is only for testing purposes - in production, always require authentication or user_id

        // Check if coupon has reached its usage limit
        $totalUsage = Order::where('coupon_id', $coupon->id)->count();

        // For debugging purposes, log the coupon usage details with more information
        \Log::info('Coupon usage check (DETAILED)', [
            'coupon_id' => $coupon->id,
            'coupon_code' => $coupon->code,
            'total_usage' => $totalUsage,
            'usage_limit' => $coupon->usage_limit,
            'has_reached_limit' => ($totalUsage >= $coupon->usage_limit),
            'comparison' => "{$totalUsage} >= {$coupon->usage_limit}",
            'raw_usage_limit' => $coupon->getRawOriginal('usage_limit'),
            'usage_limit_type' => gettype($coupon->usage_limit),
            'total_usage_type' => gettype($totalUsage),
        ]);

        // Make sure we're comparing integers
        $usageLimit = (int) $coupon->usage_limit;
        $totalUsageCount = (int) $totalUsage;

        if ($totalUsageCount >= $usageLimit) {
            return [
                'valid' => false,
                'message' => __('message.coupon_usage_limit_reached'),
            ];
        }

        // Coupon is valid
        return [
            'valid' => true,
            'message' => __('message.coupon_valid'),
            'coupon' => $coupon,
            'usage_count' => $totalUsage,
        ];
    }
}
