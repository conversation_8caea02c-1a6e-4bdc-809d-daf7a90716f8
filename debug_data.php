<?php
require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Debug البيانات ===" . PHP_EOL;

// فحص البيانات
$about_sections = DB::table('landings')->where('section', 'landing_abouts')->get();
echo "about_sections count: " . count($about_sections) . PHP_EOL;
foreach($about_sections as $section) {
    echo "- ID: " . $section->id . ", Title: " . $section->title . PHP_EOL;
}

$howitworks = DB::table('landings')->where('section', 'how_it_works')->get();
echo "howitworks count: " . count($howitworks) . PHP_EOL;
foreach($howitworks as $work) {
    echo "- ID: " . $work->id . ", Title: " . $work->title . PHP_EOL;
}

$app_features = DB::table('app_features')->get();
echo "app_features count: " . count($app_features) . PHP_EOL;

// فحص اللغة الحالية
$current_locale = 'ar';
$current_language = DB::table('languages')->where('short', $current_locale)->first();
echo "current_language: " . ($current_language ? $current_language->name : 'NULL') . PHP_EOL;
?>