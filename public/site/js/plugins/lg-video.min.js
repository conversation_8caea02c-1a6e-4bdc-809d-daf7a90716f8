/*! lg-video - v1.2.0 - 2017-11-16
* http://sachinchoolur.github.io/lightGallery
* Copyright (c) 2017 Sachin N; Licensed GPLv3 */

(function(root,factory){if(typeof define==='function'&&define.amd){define(['jquery'],function(a0){return(factory(a0))})}else if(typeof exports==='object'){module.exports=factory(require('jquery'))}else{factory(jQuery)}}(this,function($){(function(){'use strict';var defaults={videoMaxWidth:'855px',autoplayFirstVideo:!0,youtubePlayerParams:!1,vimeoPlayerParams:!1,dailymotionPlayerParams:!1,vkPlayerParams:!1,videojs:!1,videojsOptions:{}};var Video=function(element){this.core=$(element).data('lightGallery');this.$el=$(element);this.core.s=$.extend({},defaults,this.core.s);this.videoLoaded=!1;this.init();return this};Video.prototype.init=function(){var _this=this;_this.core.$el.on('hasVideo.lg.tm',onHasVideo.bind(this));_this.core.$el.on('onAferAppendSlide.lg.tm',onAferAppendSlide.bind(this));if(_this.core.doCss()&&(_this.core.$items.length>1)&&(_this.core.s.enableSwipe||_this.core.s.enableDrag)){_this.core.$el.on('onSlideClick.lg.tm',function(){var $el=_this.core.$slide.eq(_this.core.index);_this.loadVideoOnclick($el)})}else{_this.core.$slide.on('click.lg',function(){_this.loadVideoOnclick($(this))})}
_this.core.$el.on('onBeforeSlide.lg.tm',onBeforeSlide.bind(this));_this.core.$el.on('onAfterSlide.lg.tm',function(event,prevIndex){_this.core.$slide.eq(prevIndex).removeClass('lg-video-playing')})};Video.prototype.loadVideo=function(src,addClass,noPoster,index,html){var video='';var autoplay=1;var a='';var isVideo=this.core.isVideo(src,index)||{};if(noPoster){if(this.videoLoaded){autoplay=0}else{autoplay=this.core.s.autoplayFirstVideo?1:0}}
if(isVideo.youtube){a='?wmode=opaque&autoplay='+autoplay+'&enablejsapi=1';if(this.core.s.youtubePlayerParams){a=a+'&'+$.param(this.core.s.youtubePlayerParams)}
video='<iframe class="lg-video-object lg-youtube '+addClass+'" width="560" height="315" src="//www.youtube.com/embed/'+isVideo.youtube[1]+a+'" frameborder="0" allowfullscreen></iframe>'}else if(isVideo.vimeo){a='?autoplay='+autoplay+'&api=1';if(this.core.s.vimeoPlayerParams){a=a+'&'+$.param(this.core.s.vimeoPlayerParams)}
video='<iframe class="lg-video-object lg-vimeo '+addClass+'" width="560" height="315"  src="//player.vimeo.com/video/'+isVideo.vimeo[1]+a+'" frameborder="0" webkitAllowFullScreen mozallowfullscreen allowFullScreen></iframe>'}else if(isVideo.dailymotion){a='?wmode=opaque&autoplay='+autoplay+'&api=postMessage';if(this.core.s.dailymotionPlayerParams){a=a+'&'+$.param(this.core.s.dailymotionPlayerParams)}
video='<iframe class="lg-video-object lg-dailymotion '+addClass+'" width="560" height="315" src="//www.dailymotion.com/embed/video/'+isVideo.dailymotion[1]+a+'" frameborder="0" allowfullscreen></iframe>'}else if(isVideo.html5){var fL=html.substring(0,1);if(fL==='.'||fL==='#'){html=$(html).html()}
video=html}else if(isVideo.vk){a='&autoplay='+autoplay;if(this.core.s.vkPlayerParams){a=a+'&'+$.param(this.core.s.vkPlayerParams)}
video='<iframe class="lg-video-object lg-vk '+addClass+'" width="560" height="315" src="http://vk.com/video_ext.php?'+isVideo.vk[1]+a+'" frameborder="0" allowfullscreen></iframe>'}
return video};Video.prototype.loadVideoOnclick=function($el){var _this=this;if($el.find('.lg-object').hasClass('lg-has-poster')&&$el.find('.lg-object').is(':visible')){if(!$el.hasClass('lg-has-video')){$el.addClass('lg-video-playing lg-has-video');var _src;var _html;var _loadVideo=function(_src,_html){$el.find('.lg-video').append(_this.loadVideo(_src,'',!1,_this.core.index,_html));if(_html){if(_this.core.s.videojs){try{videojs(_this.core.$slide.eq(_this.core.index).find('.lg-html5').get(0),_this.core.s.videojsOptions,function(){this.play()})}catch(e){console.error('Make sure you have included videojs')}}else{_this.core.$slide.eq(_this.core.index).find('.lg-html5').get(0).play()}}};if(_this.core.s.dynamic){_src=_this.core.s.dynamicEl[_this.core.index].src;_html=_this.core.s.dynamicEl[_this.core.index].html;_loadVideo(_src,_html)}else{_src=_this.core.$items.eq(_this.core.index).attr('href')||_this.core.$items.eq(_this.core.index).attr('data-src');_html=_this.core.$items.eq(_this.core.index).attr('data-html');_loadVideo(_src,_html)}
var $tempImg=$el.find('.lg-object');$el.find('.lg-video').append($tempImg);if(!$el.find('.lg-video-object').hasClass('lg-html5')){$el.removeClass('lg-complete');$el.find('.lg-video-object').on('load.lg error.lg',function(){$el.addClass('lg-complete')})}}else{var youtubePlayer=$el.find('.lg-youtube').get(0);var vimeoPlayer=$el.find('.lg-vimeo').get(0);var dailymotionPlayer=$el.find('.lg-dailymotion').get(0);var html5Player=$el.find('.lg-html5').get(0);if(youtubePlayer){youtubePlayer.contentWindow.postMessage('{"event":"command","func":"playVideo","args":""}','*')}else if(vimeoPlayer){try{$f(vimeoPlayer).api('play')}catch(e){console.error('Make sure you have included froogaloop2 js')}}else if(dailymotionPlayer){dailymotionPlayer.contentWindow.postMessage('play','*')}else if(html5Player){if(_this.core.s.videojs){try{videojs(html5Player).play()}catch(e){console.error('Make sure you have included videojs')}}else{html5Player.play()}}
$el.addClass('lg-video-playing')}}};Video.prototype.destroy=function(){this.videoLoaded=!1};function onHasVideo(event,index,src,html){var _this=this;_this.core.$slide.eq(index).find('.lg-video').append(_this.loadVideo(src,'lg-object',!0,index,html));if(html){if(_this.core.s.videojs){try{videojs(_this.core.$slide.eq(index).find('.lg-html5').get(0),_this.core.s.videojsOptions,function(){if(!_this.videoLoaded&&_this.core.s.autoplayFirstVideo){this.play()}})}catch(e){console.error('Make sure you have included videojs')}}else{if(!_this.videoLoaded&&_this.core.s.autoplayFirstVideo){_this.core.$slide.eq(index).find('.lg-html5').get(0).play()}}}}
function onAferAppendSlide(event,index){var $videoCont=this.core.$slide.eq(index).find('.lg-video-cont');if(!$videoCont.hasClass('lg-has-iframe')){$videoCont.css('max-width',this.core.s.videoMaxWidth);this.videoLoaded=!0}}
function onBeforeSlide(event,prevIndex,index){var _this=this;var $videoSlide=_this.core.$slide.eq(prevIndex);var youtubePlayer=$videoSlide.find('.lg-youtube').get(0);var vimeoPlayer=$videoSlide.find('.lg-vimeo').get(0);var dailymotionPlayer=$videoSlide.find('.lg-dailymotion').get(0);var vkPlayer=$videoSlide.find('.lg-vk').get(0);var html5Player=$videoSlide.find('.lg-html5').get(0);if(youtubePlayer){youtubePlayer.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}','*')}else if(vimeoPlayer){try{$f(vimeoPlayer).api('pause')}catch(e){console.error('Make sure you have included froogaloop2 js')}}else if(dailymotionPlayer){dailymotionPlayer.contentWindow.postMessage('pause','*')}else if(html5Player){if(_this.core.s.videojs){try{videojs(html5Player).pause()}catch(e){console.error('Make sure you have included videojs')}}else{html5Player.pause()}}if(vkPlayer){$(vkPlayer).attr('src',$(vkPlayer).attr('src').replace('&autoplay','&noplay'))}
var _src;if(_this.core.s.dynamic){_src=_this.core.s.dynamicEl[index].src}else{_src=_this.core.$items.eq(index).attr('href')||_this.core.$items.eq(index).attr('data-src')}
var _isVideo=_this.core.isVideo(_src,index)||{};if(_isVideo.youtube||_isVideo.vimeo||_isVideo.dailymotion||_isVideo.vk){_this.core.$outer.addClass('lg-hide-download')}}
$.fn.lightGallery.modules.video=Video})()}))