(function () {     



jQuery("#rev_slider_9_1").show().revolution({  

    jsFileLocation:"js/",
    visibilityLevels:"1240,1024,768,480",
    gridwidth:1160,
    gridheight:612,
    minHeight:"",
    spinner:"spinner3",
    responsiveLevels:"1240,1024,768,480",
    disableProgressBar:"on",
    navigation: {
        onHoverStop:false
    },
    fallbacks: {
        allowHTML5AutoPlayOnAndroid:true
    },
});


jQuery("#rev_slider_10_1").show().revolution({  
        jsFileLocation:"js/",
        sliderLayout:"fullwidth",
        visibilityLevels:"1240,1024,778,480",
        gridwidth:"1920,1200,778,480",
        gridheight:"1200,1000,900,800",
        minHeight:1200,
        spinner:"spinner3",
        responsiveLevels:"1240,1024,778,480",
        disableProgressBar:"on",
        navigation: {
            mouseScrollNavigation:false,
            onHoverStop:false,
            arrows: {
                enable:true,
                style:"uranus",
                hide_onleave:true,
                hide_delay:300,
                left: {

                },
                right: {

                }
            }
        },
        parallax: {
            levels:[5,10,15,20,25,30,35,40,45,46,47,48,49,50,51,55],
            type:"mouse"
        },
        fallbacks: {
            allowHTML5AutoPlayOnAndroid:true
        },
});
    
jQuery("#rev_slider_4_1").show().revolution({  
    jsFileLocation:"js/",
    visibilityLevels:"1240,1024,778,480",
    gridwidth:951,
    gridheight:819,
    minHeight:"",
    spinner:"spinner3",
    responsiveLevels:"1240,1024,778,480",
    disableProgressBar:"on",
    navigation: {
        onHoverStop:false
    },
    fallbacks: {
        allowHTML5AutoPlayOnAndroid:true
    },
});

    
jQuery("#rev_slider_11_1").show().revolution({  
    jsFileLocation:"js/",
    sliderLayout:"fullwidth",
    visibilityLevels:"1240,1024,778,480",
    gridwidth:"1430,1024,778,480",
    gridheight:"1000,900,800,800",
    minHeight:1000,
    spinner:"spinner3",
    responsiveLevels:"1240,1024,778,480",
    disableProgressBar:"on",
    navigation: {
        mouseScrollNavigation:false,
        onHoverStop:false,
        arrows: {
            enable:true,
            tmp:"<div class=\"tp-title-wrap\">  	<div class=\"tp-arr-imgholder\"></div> </div>",
            style:"zeus",
            hide_onleave:true,
            left: {

            },
            right: {

            }
        }
    },
    fallbacks: {
        allowHTML5AutoPlayOnAndroid:true
    },
});


    
jQuery("#rev_slider_3_1").show().revolution({  
    jsFileLocation:"js/",
    sliderLayout:"fullwidth",
    visibilityLevels:"1240,1024,778,480",
    gridwidth:"1920,1024,778,480",
    gridheight:"1075,900,800,700",
    minHeight:1075,
    spinner:"spinner3",
    responsiveLevels:"1240,1024,778,480",
    disableProgressBar:"on",
    navigation: {
        onHoverStop:false
    },
    fallbacks: {
        allowHTML5AutoPlayOnAndroid:true
    },
});

    
jQuery("#rev_slider_2_1").show().revolution({  
    jsFileLocation:"js/",
    sliderLayout:"fullwidth",
    visibilityLevels:"1240,1024,778,480",
    gridwidth:"1600,1024,778,480",
    gridheight:"720,720,720,720",
    minHeight:720,
    spinner:"spinner3",
    responsiveLevels:"1240,1024,778,480",
    disableProgressBar:"on",
    navigation: {
        mouseScrollNavigation:false,
        onHoverStop:false,
        arrows: {
            enable:true,
            style:"uranus",
            hide_onleave:true,
            left: {

            },
            right: {

            }
        }
    },
    fallbacks: {
        allowHTML5AutoPlayOnAndroid:true
    },
});


    
jQuery("#rev_slider_7_1").show().revolution({  
    jsFileLocation:"js/",
    visibilityLevels:"1240,1024,778,480",
    gridwidth:"1720,1024,778,480",
    gridheight:"955,900,800,800",
    minHeight:800,
    spinner:"spinner3",
    responsiveLevels:"1240,1024,778,480",
    disableProgressBar:"on",
    navigation: {
        mouseScrollNavigation:false,
        onHoverStop:false,
        arrows: {
            enable:true,
            tmp:"<div class=\"tp-title-wrap\">  	<div class=\"tp-arr-imgholder\"></div> </div>",
            style:"zeus",
            hide_onleave:true,
            hide_delay:300,
            left: {

            },
            right: {

            }
        }
    },
    fallbacks: {
        allowHTML5AutoPlayOnAndroid:true
    },
});


    
jQuery("#rev_slider_12_1").show().revolution({  
    jsFileLocation:"js/",
    sliderLayout:"fullwidth",
    visibilityLevels:"1240,1024,778,480",
    gridwidth:"1640,1024,778,480",
    gridheight:"1000,900,800,800",
    minHeight:1000,
    spinner:"spinner3",
    responsiveLevels:"1240,1024,778,480",
    disableProgressBar:"on",
    navigation: {
        mouseScrollNavigation:false,
        onHoverStop:false,
        arrows: {
            enable:true,
            tmp:"<div class=\"tp-title-wrap\">  	<div class=\"tp-arr-imgholder\"></div> </div>",
            style:"zeus",
            left: {

            },
            right: {

            }
        }
    },
    fallbacks: {
        allowHTML5AutoPlayOnAndroid:true
    },
});


    
jQuery("#rev_slider_1_1").show().revolution({  
    jsFileLocation:"js/",
    sliderLayout:"fullwidth",
    visibilityLevels:"1240,1024,778,480",
    gridwidth:"1920,1024,778,480",
    gridheight:"700,700,700,700",
    minHeight:700,
    spinner:"spinner3",
    responsiveLevels:"1240,1024,778,480",
    disableProgressBar:"on",
    navigation: {
        onHoverStop:false
    },
    viewPort: {
        enable:true,
        visible_area:"20%"
    },
    fallbacks: {
        allowHTML5AutoPlayOnAndroid:true
    },
});


    
jQuery("#rev_slider_8_1").show().revolution({  
    jsFileLocation:"js/",
    visibilityLevels:"1240,1024,778,480",
    gridwidth:1106,
    gridheight:604,
    minHeight:"",
    spinner:"spinner3",
    responsiveLevels:"1240,1024,778,480",
    disableProgressBar:"on",
    navigation: {
        onHoverStop:false
    },
    viewPort: {
        enable:true,
        visible_area:"20%"
    },
    fallbacks: {
        allowHTML5AutoPlayOnAndroid:true
    },
});





})(jQuery);


