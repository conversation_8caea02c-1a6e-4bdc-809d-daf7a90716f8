<!DOCTYPE html>
<html class="no-js" lang="{{ app()->getLocale() }}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{ config('app.name') }}</title>
    <meta name="description" content="{{ config('app.name') }}">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="icon" href="{{ asset('images/logo-en.png') }}">
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.7.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('site/css/style.css') }}">
    @if($current_language && $current_language->direction == 'rtl')
    <link rel="stylesheet" href="{{ asset('site/css/rtl.css') }}">
    @endif
</head>

<body dir="{{ $current_language->direction ?? 'ltr' }}">
    <div id="main-wrapper">
        <div class="container-fluid py-2 px-4" id="language_drop_bar">
            <div class="d-flex justify-content-end">
                <div class="dropdown">
                    <button class="btn btn-outline-primary btn-sm dropdown-toggle d-flex align-items-center" 
                            type="button" 
                            id="languageDropdown" 
                            data-bs-toggle="dropdown" 
                            aria-expanded="false">
                        <i class="fas fa-globe me-2"></i>
                        <span>{{ $current_language->name ?? 'English' }}</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
                        @if(isset($other_languages) && count($other_languages) > 0)
                            @foreach ($other_languages as $language)
                                <li>
                                    <a class="dropdown-item d-flex align-items-center" 
                                       href="{{ url('/'.$language->short) }}">
                                        <span class="me-2">🌐</span>
                                        {{ $language->name }}
                                    </a>
                                </li>
                            @endforeach
                        @else
                            <li><span class="dropdown-item-text text-muted">No other languages available</span></li>
                        @endif
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="site-wrapper-reveal">
            <div class="start-ups-hero__slider position-relative section-space--pt_60">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="start-ups-hero__content">
                                <div class="row">
                                    <div class="col-lg-7 text-center order-2 order-lg-1">
                                        <div class="start-ups-hero--images__midea">
                                            @if($current_language && $current_language->short == 'ar')
                                                <img src="{{ asset('images/logo-ar.png') }}" alt="{{ config('app.name') }}" class="img-fluid">
                                            @else
                                                <img src="{{ asset('images/logo-en.png') }}" alt="{{ config('app.name') }}" class="img-fluid">
                                            @endif
                                        </div>
                                    </div>
                                    <div class="col-lg-5 order-1 order-lg-2">
                                        <div class="start-ups-hero__text section-space--pt_220">
                                            <h1 class="text-center">{{ $landing_slider->title ?? config('app.name') }}</h1>
                                            <p class="text-hero section-space--mb_40 text-center">
                                                {{ $landing_slider->description ?? 'Welcome to our service platform' }}
                                            </p>
                                            <div class="hero-btn-wrap justify-content-center">
                                                @if($ios_app && $ios_app->value)
                                                <a href="{{ $ios_app->value }}" target="_blank" class="mx-1">
                                                    <img src="{{ asset('site/images/app/ios-store.png') }}" alt="App Store" />
                                                </a>
                                                @endif
                                                @if($android_app && $android_app->value)
                                                <a href="{{ $android_app->value }}" target="_blank" class="mx-1">
                                                    <img src="{{ asset('site/images/app/google-play.png') }}" alt="Google Play" />
                                                </a>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if(isset($about_sections) && count($about_sections) > 0)
    <section id="about_home_section">
        <div class="container-fluid">
            <h2><span>About Our Service</span></h2>
            <div class="row">
                @foreach ($about_sections as $section)
                    <div class="col-6 col-lg-3">
                        <div class="instructions_items">
                            <img src="{{ Storage::disk('public')->url($section->image ?? 'default.png') }}">
                            <p>{{ $section->title ?? 'Service' }}</p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <section id="footer">
        <div class="container">
            <div class="row" style="text-align: center;">
                <div class="col-12 col-lg-6">
                    <h2>Contact Us</h2>
                    <p>Welcome to {{ config('app.name') }}</p>
                </div>
                <div class="col-12 col-lg-6">
                    <h2>Get Our App</h2>
                    <div class="app-links d-flex justify-content-center">
                        @if($ios_app && $ios_app->value)
                        <a href="{{ $ios_app->value }}" target="_blank" class="mx-1">
                            <img src="{{ asset('site/images/app/ios-store.png') }}" alt="App Store" />
                        </a>
                        @endif
                        @if($android_app && $android_app->value)
                        <a href="{{ $android_app->value }}" target="_blank" class="mx-1">
                            <img src="{{ asset('site/images/app/google-play.png') }}" alt="Google Play" />
                        </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>

    @if(isset($socials) && count($socials) > 0)
    <div class="text-center py-3">
        <ul class="footer_social list-inline">
            @foreach ($socials as $social)
                <li class="list-inline-item">
                    <a href="{{ $social->url }}" target="_blank">
                        <i class="fab fa-{{ $social->name }}"></i>
                    </a>
                </li>
            @endforeach
        </ul>
    </div>
    @endif

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Language Switcher Enhancement -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth transition for language switching
            const languageLinks = document.querySelectorAll('#languageDropdown + .dropdown-menu a');
            languageLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Add loading indicator
                    const button = document.getElementById('languageDropdown');
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
                    button.disabled = true;
                    
                    // Allow navigation after brief delay
                    setTimeout(() => {
                        window.location.href = this.href;
                    }, 300);
                });
            });
        });
    </script>
    
    <style>
        #language_drop_bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1050;
        }
        
        .dropdown {
            position: relative;
            z-index: 1051;
        }
        
        #languageDropdown {
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 14px;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1052;
        }
        
        #languageDropdown:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .dropdown-menu {
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border: none;
            padding: 8px 0;
            z-index: 1053 !important;
            position: absolute !important;
        }
        
        .dropdown-item {
            padding: 10px 20px;
            transition: all 0.2s ease;
        }
        
        .dropdown-item:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }
    </style>
</body>
</html>