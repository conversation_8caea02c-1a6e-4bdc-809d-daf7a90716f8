<!DOCTYPE html>
<html class="no-js" lang="{{ app()->getLocale() }}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{ config('app.name') }}</title>
    <meta name="description" content="{{ config('app.name') }}">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- Favicon -->
    <link rel="icon" href="{{ asset('images/logo-en.png') }}">

    <!-- CSS
        ============================================ -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.7.0/css/all.min.css" integrity="sha512-gRH0EcIcYBFkQTnbpO8k0WlsD20x5VzjhOA1Og8+ZUAhcMUCvd+APD35FJw3GzHAP3e+mP28YcDJxVr745loHw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="{{ asset('site/css/revolution/rs6.css') }}">
    <link rel="stylesheet" href="{{ asset('site/css/plugins/vendor.min.css') }}">
    <link rel="stylesheet" href="{{ asset('site/css/plugins/plugins.min.css') }}">
    <!-- Main Style CSS -->
    <link rel="stylesheet" href="{{ asset('site/css/style.css') }}">
    @if($current_language->direction == 'rtl')
    <link rel="stylesheet" href="{{ asset('site/css/rtl.css') }}">
    @endif
</head>

<body dir="{{ $current_language->direction }}">
    <div id="main-wrapper">
        <div class="container-fluid py-1 px-5" id="language_drop_bar">
            <div class="dropdown">
                <ul class="nav nav-tabs">

  <li class="nav-item dropdown">
                <button class="nav-link btn btn-light btn-sm dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false" type="button" id="LanguageButton" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                  {{ $current_language->name }}
                </button>
                <div class="dropdown-menu" aria-labelledby="LanguageButton">
                    @foreach ($other_languages as $language)
                        <a class="dropdown-item" href="{{ url('/'.$language->short) }}">{{ $language->name }}</a>
                    @endforeach
                </div>

  </li>
  </ul>
              </div>
        </div>

        <div class="site-wrapper-reveal">
            <div class="start-ups-hero__slider start-ups-hero--bg__images  position-relative  section-space--pt_60">
                <div class="container-fluid container-fluid--cp-150 ">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="start-ups-hero__content">
                                <div class="row">
                                    <div class="col-lg-7 text-center order-2 order-lg-1">
                                        <div class="start-ups-hero--images__midea">
                                            @if($current_language->short == 'ar')
                                            <img src="{{ asset('images/logo-ar.png') }}" alt="{{ $landing_slider->title }}" class="img-fluid slider-01">
                                            @else
                                            <img src="{{ asset('images/logo-en.png') }}" alt="{{ $landing_slider->title }}" class="img-fluid slider-01">
                                            @endif
                                        </div>
                                    </div>
                                    <div class="col-lg-5 order-1 order-lg-2">
                                        <div class="start-ups-hero__text section-space--pt_220">
                                            <h1 class="text-center">{{ $landing_slider->title }}</h1>
                                            <p class="text-hero section-space--mb_40 text-center">{{ $landing_slider->description }}</p>
                                            <div class="hero-btn-wrap justify-content-center">
                                                <a href="{{ $ios_app->value }}" target="_blank" class="mx-1">
                                                    <img src="{{ asset('site/images/app/ios-store.png') }}" alt="App Store" />
                                                </a>
                                                <a href="{{ $android_app->value }}" target="_blank" class="mx-1">
                                                    <img src="{{ asset('site/images/app/google-play.png') }}" alt="Google Play" />
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--====================  banner background End ====================-->
        </div>
    </div>

    <section id="about_home_section">
        <div class="container-fluid">
            <h2><span>{{ __('About Vish Vish') }}</span></h2>
            <div class="row">
                @foreach ($about_sections as $section)
                    <div class="col-6 col-lg-3">
                        <div class="instructions_items">
                            <img src="{{ Storage::disk('public')->url($section->image) }}"></i>
                            <p>{{ $section->title }}</p>
                        </div>
                    </div>
                @endforeach

            </div>
        </div>
    </section>

    <section id="portfolio_section">
        <div class="container-fluid">
            <!-- Swiper -->
            <div class="swiper-container two">
                <div class="swiper-wrapper">
                    @foreach ($screens as $screen)
                        <div class="swiper-slide">
                            <div class="project-block-two portfolio_mobile_overlay">
                                <div class="image-box">
                                    <figure class="image">
                                        <img src="{{Storage::disk('public')->url($screen->image)}}" alt="Screen {{ $loop->iteration }}">
                                    </figure>
                                </div>
                            </div>
                        </div>
                    @endforeach
                    <!-- Add Pagination -->
                </div>
            </div>
        </div>
    </section>

    <section id="steps_section">
        <div class="container">
            <div class="row" dir="ltr">
                <div class="col-12 col-lg-4">
                    @foreach ($app_features_one as $feature)
                        <div class="step_info">
                            <h3>{{ $feature->title }}</h3>
                            <p>{{ $feature->text }}</p>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>

    <section id="howitworks_section">
        <div class="container">
            <h2><span>{{ __('How Vish Vish Work') }}</span></h2>
            <div class="row">
                @foreach ($howitworks as $section)
                <div class="col-6 col-lg-4">
                    <div class="howitworks_items">
                        <img src="{{ Storage::disk('public')->url($section->image) }}" alt="{{ $section->title }}" />
                        <h3>{{ $section->title }}</h3>
                        <p>{{ $section->description }}</p>
                    </div>
                </div>
                @endforeach

            </div>
        </div>
    </section>

    <section id="footer">
        <div class="container">
            <div class="row" style="text-align: center;">
                <div class="col-12 col-lg-4">
                    <h2>{{ __('Contact Us') }}</h2>
                    <p><i class="fas fa-phone"></i> {{ $contact_phone->value ?? '' }}</p>
                    <p><i class="fas fa-envelope"></i> {{ $contact_email->value ?? '' }}</p>
                    <ul class="footer_social">
                        @foreach ($socials as $social)
                            <li>
                                <a href="{{ $social->url }}" target="_blank"><i class="fab fa-{{ $social->name }}"></i></a>
                            </li>
                        @endforeach
                    </ul>
                </div>
                <div class="col-12 col-lg-4">
                    <h2>{{ __('Get Vish Vish App') }}</h2>
                    <div class="app-links d-flex justify-content-center">
                        <a href="{{ $ios_app->value }}" target="_blank" class="mx-1">
                            <img src="{{ asset('site/images/app/ios-store.png') }}" alt="App Store" />
                        </a>
                        <a href="{{ $android_app->value }}" target="_blank" class="mx-1">
                            <img src="{{ asset('site/images/app/google-play.png') }}" alt="Google Play" />
                        </a>
                    </div>
                </div>
                <div class="col-12 col-lg-4 text-end">
                    {{--<img src="{{ asset('site/images/OBJECTS.png') }}" alt="LOGO" />--}}
                </div>
            </div>
        </div>
    </section>

    <!--====================  scroll top ====================-->
    <a href="#" class="scroll-top" id="scroll-top">
        <i class="arrow-top fal fa-long-arrow-up"></i>
        <i class="arrow-bottom fal fa-long-arrow-up"></i>
    </a>
    <!-- JS
    ============================================ -->
    <!-- jquery -->
    <script src="{{ asset('site/js/plugins/jquery-3.5.1.min.js') }}"></script>
    <script src="{{ asset('site/js/plugins/jquery-migrate-3.3.0.min.js') }}"></script>

    <!-- Bootstrap JS -->
    <script src="{{ asset('site/js/plugins/bootstrap.min.js') }}"></script>
    <!-- Swiper Slider JS -->
    <script rel="preload" src="https://cdnjs.cloudflare.com/ajax/libs/Swiper/3.4.1/js/swiper.min.js"></script>
    <script>
        var swiper = new Swiper('.swiper-container.two', {
            paginationClickable: false,
            effect: 'coverflow',
            loop: true,
            centeredSlides: true,
            slidesPerView: 'auto',
            coverflow: {
                rotate: 0,
                stretch: 0,
                depth: 50,
                modifier: 1.5,
                slideShadows: false,
            }
        });
    </script>
    <!-- Tippy JS -->
    <script src="{{ asset('site/js/plugins/tippy.min.js') }}"></script>

    <!-- Light gallery JS -->
    <script src="{{ asset('site/js/plugins/lightgallery.min.js') }}"></script>

    <!-- Light gallery video JS -->
    <script src="{{ asset('site/js/plugins/lg-video.min.js') }}"></script>

    <!-- Waypoints JS -->
    <script src="{{ asset('site/js/plugins/waypoints.min.js') }}"></script>

    <!-- Counter down JS -->
    <script src="{{ asset('site/js/plugins/countdown.min.js') }}"></script>

    <!-- Counter down JS -->
    <script src="{{ asset('site/js/plugins/odometer.min.js') }}"></script>

    <!-- Isotope JS -->
    <script src="{{ asset('site/js/plugins/isotope.min.js') }}"></script>

    <!-- Masonry JS -->
    <script src="{{ asset('site/js/plugins/masonry.min.js') }}"></script>

    <!-- ImagesLoaded JS -->
    <script src="{{ asset('site/js/plugins/images-loaded.min.js') }}"></script>

    <!-- Appear JS -->
    <script src="{{ asset('site/js/plugins/appear.min.js') }}"></script>

    <!-- TweenMax JS -->
    <script src="{{ asset('site/js/plugins/TweenMax.min.js') }}"></script>

    <!-- Wavify JS -->
    <script src="{{ asset('site/js/plugins/wavify.js') }}"></script>

    <!-- jQuery Wavify JS -->
    <script src="{{ asset('site/js/plugins/jquery.wavify.js') }}"></script>

    <!-- circle progress JS -->
    <script src="{{ asset('site/js/plugins/circle-progress.min.js') }}"></script>

    <!-- counterup JS -->
    <script src="{{ asset('site/js/plugins/counterup.min.js') }}"></script>

    <!-- instafeed JS -->
    <script src="{{ asset('site/js/plugins/instafeed.min.js') }}"></script>

    <!-- wow JS -->
    <script src="{{ asset('site/js/plugins/wow.min.js') }}"></script>

    <!-- time circles JS -->
    <script src="{{ asset('site/js/plugins/time-circles.js') }}"></script>

    <!-- animation text JS -->
    <script src="{{ asset('site/js/plugins/animation-text.min.js') }}"></script>

    <!-- one page nav JS -->
    <script src="{{ asset('site/js/plugins/one-page-nav.min.js') }}"></script>

    <!-- Revolution JS -->
    <script src="{{ asset('site/js/revolution/revolution.tools.min.js') }}"></script>
    <script src="{{ asset('site/js/revolution/rs6.min.js') }}"></script>
    <script src="{{ asset('site/js/revolution.js') }}"></script>

    <!-- Main JS -->
    <script src="{{ asset('site/js/main.js') }}"></script>


</body>
</html>
